import turtle
import math

def draw_star(t, center_x, center_y, outer_radius, point_angle_degrees=90):
    """
    Draws a five-pointed star with one point facing the specified direction.
    t: turtle object
    center_x, center_y: center coordinates of the star
    outer_radius: radius of the circle passing through the outer points of the star
    point_angle_degrees: angle in degrees for one point of the star to face (0 is right, 90 is up)
    """
    t.penup() # Lift the pen
    t.color("yellow")

    # Calculate the five points of the star
    points = []
    for i in range(5):
        # Each point is 72 degrees apart (360/5 = 72)
        angle = math.radians(point_angle_degrees + i * 72)
        x = center_x + outer_radius * math.cos(angle)
        y = center_y + outer_radius * math.sin(angle)
        points.append((x, y))

    # Start drawing from the first point
    t.goto(points[0][0], points[0][1])
    t.pendown()
    t.begin_fill()

    # Draw the star by connecting every second point
    # This creates the classic five-pointed star shape
    for i in range(5):
        next_point_index = (i * 2) % 5
        t.goto(points[next_point_index][0], points[next_point_index][1])

    t.end_fill()
    t.penup() # Lift the pen after drawing

def main():
    # Flag dimensions
    FLAG_WIDTH = 900
    FLAG_HEIGHT = 600

    # Colors
    FLAG_RED = "#EE1C25" # Standard Chinese Flag Red
    FLAG_YELLOW = "#FFFF00" # Standard Chinese Flag Yellow

    # Setup screen
    screen = turtle.Screen()
    screen.setup(width=FLAG_WIDTH + 50, height=FLAG_HEIGHT + 50) # Add some padding
    screen.bgcolor("lightgray") # Background for visibility

    # Use default coordinate system where (0,0) is the center of the screen

    # Create turtle
    pen = turtle.Turtle()
    pen.speed(0) # Fastest speed
    pen.hideturtle()

    # 1. Draw red flag background
    # Adjust coordinates for default center (0,0)
    # Top-left corner of the flag will be (-FLAG_WIDTH/2, FLAG_HEIGHT/2)
    pen.penup()
    pen.goto(-FLAG_WIDTH / 2, FLAG_HEIGHT / 2)
    pen.pendown()
    pen.color(FLAG_RED)
    pen.begin_fill()
    for _ in range(2):
        pen.forward(FLAG_WIDTH)
        pen.right(90)
        pen.forward(FLAG_HEIGHT)
        pen.right(90)
    pen.end_fill()
    pen.penup()

    # 2. Draw the big star
    BIG_STAR_OUTER_RADIUS = FLAG_HEIGHT * 3 / 10 / 2 # Diameter is 3/10 of height

    # Big star center based on official grid (5,5) on a 30x20 grid
    # Convert to default coordinate system (center is 0,0)
    BIG_STAR_CENTER_X_OLD = FLAG_WIDTH * 5 / 30 # 150
    BIG_STAR_CENTER_Y_OLD = FLAG_HEIGHT * 5 / 20 # 150

    BIG_STAR_CENTER_X = BIG_STAR_CENTER_X_OLD - FLAG_WIDTH / 2 # 150 - 450 = -300
    BIG_STAR_CENTER_Y = FLAG_HEIGHT / 2 - BIG_STAR_CENTER_Y_OLD # 300 - 150 = 150

    # The big star's top point should be pointing upwards (90 degrees)
    draw_star(pen, BIG_STAR_CENTER_X, BIG_STAR_CENTER_Y, BIG_STAR_OUTER_RADIUS, point_angle_degrees=90)

    # 3. Draw the four small stars
    SMALL_STAR_OUTER_RADIUS = FLAG_HEIGHT / 10 / 2 # Diameter is 1/10 of height

    # Small star positions based on official grid
    small_stars_data_old = [
        {"center_x": FLAG_WIDTH * 10 / 30, "center_y": FLAG_HEIGHT * 1 / 20}, # (10,1) on 30x20 grid
        {"center_x": FLAG_WIDTH * 12 / 30, "center_y": FLAG_HEIGHT * 3 / 20}, # (12,3) on 30x20 grid
        {"center_x": FLAG_WIDTH * 12 / 30, "center_y": FLAG_HEIGHT * 7 / 20}, # (12,7) on 30x20 grid
        {"center_x": FLAG_WIDTH * 10 / 30, "center_y": FLAG_HEIGHT * 9 / 20}, # (10,9) on 30x20 grid
    ]

    small_stars_data = []
    for star_data_old in small_stars_data_old:
        sx_old, sy_old = star_data_old["center_x"], star_data_old["center_y"]
        sx_new = sx_old - FLAG_WIDTH / 2
        sy_new = FLAG_HEIGHT / 2 - sy_old
        small_stars_data.append({"center_x": sx_new, "center_y": sy_new})

    for star_data in small_stars_data:
        sx, sy = star_data["center_x"], star_data["center_y"]
        bx, by = BIG_STAR_CENTER_X, BIG_STAR_CENTER_Y

        # Calculate the angle from small star center to big star center
        # This will be the direction that one point of the small star should face
        angle_radians = math.atan2(by - sy, bx - sx)
        angle_degrees = math.degrees(angle_radians)

        draw_star(pen, sx, sy, SMALL_STAR_OUTER_RADIUS, point_angle_degrees=angle_degrees)

    # Keep window open
    screen.exitonclick()

if __name__ == "__main__":
    main()